#!/usr/bin/env python3
"""
Script para exportar tabelas de resultados_ate_hoje.sqlite para database.sqlite
"""

import sqlite3
import pandas as pd
from datetime import datetime

def export_tables():
    """Exporta tabelas do resultados_ate_hoje.sqlite para database.sqlite"""
    
    print("🔄 EXPORTANDO TABELAS")
    print("=" * 50)
    
    try:
        # Conectar aos dois bancos
        source_conn = sqlite3.connect('resultados_ate_hoje.sqlite')
        target_conn = sqlite3.connect('database.sqlite')
        
        # Listar tabelas do banco de origem
        cursor = source_conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"📋 Tabelas encontradas em resultados_ate_hoje.sqlite: {tables}")
        
        for table_name in tables:
            print(f"\n🔄 Processando tabela: {table_name}")
            
            # Ler dados da tabela de origem
            df = pd.read_sql_query(f"SELECT * FROM {table_name}", source_conn)
            print(f"   📊 Registros encontrados: {len(df):,}")
            
            # Definir nome da nova tabela no banco de destino
            if table_name == "Sheet1":
                new_table_name = "resultados_concursos"
            elif table_name == "Sheet2":
                new_table_name = "dicas_analises"
            else:
                new_table_name = f"importado_{table_name}"
            
            # Verificar se a tabela já existe no banco de destino
            target_cursor = target_conn.cursor()
            target_cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name=?
            """, (new_table_name,))
            
            table_exists = target_cursor.fetchone() is not None
            
            if table_exists:
                print(f"   ⚠️  Tabela '{new_table_name}' já existe no database.sqlite")
                
                # Verificar quantos registros já existem
                existing_count = pd.read_sql_query(
                    f"SELECT COUNT(*) as count FROM {new_table_name}", 
                    target_conn
                ).iloc[0]['count']
                
                print(f"   📊 Registros existentes: {existing_count:,}")
                
                # Perguntar ao usuário o que fazer
                action = input(f"   ❓ O que fazer? (s)ubstituir, (a)dicionar, (p)ular: ").lower()
                
                if action == 's':
                    # Substituir tabela
                    df.to_sql(new_table_name, target_conn, if_exists='replace', index=False)
                    print(f"   ✅ Tabela '{new_table_name}' substituída com {len(df):,} registros")
                
                elif action == 'a':
                    # Adicionar registros
                    df.to_sql(new_table_name, target_conn, if_exists='append', index=False)
                    print(f"   ✅ {len(df):,} registros adicionados à tabela '{new_table_name}'")
                
                else:
                    print(f"   ⏭️  Tabela '{new_table_name}' pulada")
                    continue
            
            else:
                # Criar nova tabela
                df.to_sql(new_table_name, target_conn, if_exists='replace', index=False)
                print(f"   ✅ Nova tabela '{new_table_name}' criada com {len(df):,} registros")
            
            # Mostrar estrutura da tabela criada/atualizada
            print(f"   📋 Colunas da tabela '{new_table_name}':")
            target_cursor.execute(f"PRAGMA table_info({new_table_name})")
            columns = target_cursor.fetchall()
            for col in columns:
                print(f"      - {col[1]} ({col[2]})")
        
        # Fechar conexões
        source_conn.close()
        target_conn.close()
        
        print(f"\n✅ EXPORTAÇÃO CONCLUÍDA!")
        print("=" * 50)
        
        # Mostrar resumo final
        show_final_summary()
        
    except Exception as e:
        print(f"❌ Erro durante a exportação: {e}")
        return False
    
    return True

def show_final_summary():
    """Mostra um resumo final do database.sqlite após a importação"""
    
    print("\n📊 RESUMO FINAL DO database.sqlite")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('database.sqlite')
        cursor = conn.cursor()
        
        # Listar todas as tabelas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        total_records = 0
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            total_records += count
            
            # Identificar tipo de tabela
            if "Planilha" in table_name:
                table_type = "🎲 Jogos Possíveis"
            elif "resultados" in table_name.lower():
                table_type = "🏆 Resultados Reais"
            elif "dicas" in table_name.lower():
                table_type = "💡 Dicas/Análises"
            elif "jogos_finalizados" in table_name:
                table_type = "✅ Controle"
            else:
                table_type = "📋 Outros"
            
            print(f"{table_type} {table_name}: {count:,} registros")
        
        print(f"\n📈 TOTAL: {len(tables)} tabelas, {total_records:,} registros")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro ao gerar resumo: {e}")

def preview_data():
    """Mostra uma prévia dos dados que serão exportados"""
    
    print("👀 PRÉVIA DOS DADOS A SEREM EXPORTADOS")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('resultados_ate_hoje.sqlite')
        
        # Sheet1 (resultados_concursos)
        print("\n🏆 Sheet1 → resultados_concursos")
        df1 = pd.read_sql_query("SELECT * FROM Sheet1 LIMIT 5", conn)
        print(df1[['id_concurso', 'resultado', 'pares_impares', 'soma_numeros_resultaado']].to_string())
        
        # Sheet2 (dicas_analises)
        print("\n💡 Sheet2 → dicas_analises")
        df2 = pd.read_sql_query("SELECT * FROM Sheet2 LIMIT 3", conn)
        print("Primeiras 3 linhas de dicas e análises...")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Erro ao mostrar prévia: {e}")

if __name__ == "__main__":
    print("🎲 EXPORTADOR DE RESULTADOS LOTO FÁCIL")
    print("=" * 50)
    print("Este script irá exportar as tabelas de resultados_ate_hoje.sqlite")
    print("para o database.sqlite principal.")
    print()
    
    # Mostrar prévia
    preview_data()
    
    print("\n" + "=" * 50)
    continuar = input("🚀 Continuar com a exportação? (s/n): ").lower()
    
    if continuar == 's':
        success = export_tables()
        if success:
            print("\n🎉 Exportação realizada com sucesso!")
            print("💡 Agora você pode usar o app.py para visualizar todos os dados!")
        else:
            print("\n❌ Exportação falhou!")
    else:
        print("❌ Exportação cancelada pelo usuário.")
