import streamlit as st
import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime
import plotly.express as px
import plotly.graph_objects as go
from io import StringIO
import re

# Configuração da página
st.set_page_config(
    page_title="Loto Fácil Database Manager",
    page_icon="🎲",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Classe para gerenciar o banco de dados
class DatabaseManager:
    def __init__(self, db_path="database.sqlite"):
        self.db_path = db_path

    def get_connection(self):
        return sqlite3.connect(self.db_path)

    def get_tables(self):
        """Retorna lista de todas as tabelas"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        conn.close()
        return tables

    def get_table_info(self, table_name):
        """Retorna informações sobre uma tabela"""
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        conn.close()
        return columns, count

    def execute_query(self, query, params=None):
        """Executa uma query SQL"""
        conn = self.get_connection()
        try:
            if params:
                result = pd.read_sql_query(query, conn, params=params)
            else:
                result = pd.read_sql_query(query, conn)
            return result
        except Exception as e:
            st.error(f"Erro na query: {e}")
            return None
        finally:
            conn.close()

    def execute_command(self, command, params=None):
        """Executa um comando SQL (INSERT, UPDATE, DELETE)"""
        conn = self.get_connection()
        cursor = conn.cursor()
        try:
            if params:
                cursor.execute(command, params)
            else:
                cursor.execute(command)
            conn.commit()
            return True
        except Exception as e:
            st.error(f"Erro no comando: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

# Inicializar o gerenciador de banco
@st.cache_resource
def init_db_manager():
    return DatabaseManager()

db_manager = init_db_manager()

# Funções auxiliares para Loto Fácil
def parse_lotofacil_numbers(resultado_str):
    """Converte string de números da Loto Fácil em lista"""
    if pd.isna(resultado_str) or resultado_str is None:
        return []
    numbers = re.findall(r'\d+', str(resultado_str))
    return [int(n) for n in numbers if 1 <= int(n) <= 25]

def validate_lotofacil_game(numbers):
    """Valida se um jogo da Loto Fácil é válido"""
    if len(numbers) != 15:
        return False, "Deve ter exatamente 15 números"
    if len(set(numbers)) != 15:
        return False, "Números não podem se repetir"
    if not all(1 <= n <= 25 for n in numbers):
        return False, "Números devem estar entre 1 e 25"
    return True, "Jogo válido"

def format_lotofacil_numbers(numbers):
    """Formata lista de números para string da Loto Fácil"""
    return ",".join([f"{n:02d}" for n in sorted(numbers)])

# Sidebar - Menu principal
st.sidebar.title("🎲 Loto Fácil Manager")
st.sidebar.markdown("---")

menu_options = [
    "📊 Dashboard",
    "🔍 Visualizar Dados",
    "➕ Inserir Dados",
    "✏️ Editar Dados",
    "🗑️ Deletar Dados",
    "🏗️ Gerenciar Tabelas",
    "📈 Análises",
    "💾 Import/Export",
    "⚙️ Configurações"
]

selected_menu = st.sidebar.selectbox("Selecione uma opção:", menu_options)

# Dashboard
if selected_menu == "📊 Dashboard":
    st.title("📊 Dashboard - Loto Fácil Database")

    # Métricas gerais
    tables = db_manager.get_tables()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total de Tabelas", len(tables))

    total_records = 0
    for table in tables:
        _, count = db_manager.get_table_info(table)
        total_records += count

    with col2:
        st.metric("Total de Registros", f"{total_records:,}")

    # Informações por tabela
    st.subheader("📋 Resumo das Tabelas")

    table_data = []
    for table in tables:
        columns, count = db_manager.get_table_info(table)
        table_data.append({
            "Tabela": table,
            "Registros": f"{count:,}",
            "Colunas": len(columns),
            "Tipo": "Jogos Loto Fácil" if "Planilha" in table else "Controle"
        })

    df_tables = pd.DataFrame(table_data)
    st.dataframe(df_tables, use_container_width=True)

    # Gráfico de distribuição de registros
    if len(table_data) > 0:
        fig = px.bar(
            df_tables,
            x="Tabela",
            y="Registros",
            title="Distribuição de Registros por Tabela",
            color="Tipo"
        )
        fig.update_layout(xaxis_tickangle=-45)
        st.plotly_chart(fig, use_container_width=True)

# Visualizar Dados
elif selected_menu == "🔍 Visualizar Dados":
    st.title("🔍 Visualizar Dados")

    tables = db_manager.get_tables()
    if not tables:
        st.warning("Nenhuma tabela encontrada no banco de dados.")
    else:
        selected_table = st.selectbox("Selecione uma tabela:", tables)

        if selected_table:
            columns, total_count = db_manager.get_table_info(selected_table)

            st.info(f"Tabela: **{selected_table}** | Total de registros: **{total_count:,}**")

            # Opções de visualização
            col1, col2, col3 = st.columns(3)

            with col1:
                limit = st.number_input("Limite de registros:", min_value=1, max_value=10000, value=100)

            with col2:
                offset = st.number_input("Pular registros:", min_value=0, value=0)

            with col3:
                if st.button("🔄 Atualizar"):
                    st.rerun()

            # Query para buscar dados
            query = f"SELECT * FROM {selected_table} LIMIT {limit} OFFSET {offset}"
            df = db_manager.execute_query(query)

            if df is not None and not df.empty:
                st.subheader(f"📋 Dados da tabela {selected_table}")

                # Filtros específicos para tabelas de Loto Fácil
                if "Planilha" in selected_table and "Resultado1" in df.columns:
                    st.subheader("🎯 Filtros Loto Fácil")

                    col1, col2 = st.columns(2)
                    with col1:
                        if "Soma" in df.columns:
                            soma_min = st.number_input("Soma mínima:", value=int(df["Soma"].min()) if not df["Soma"].isna().all() else 120)
                            soma_max = st.number_input("Soma máxima:", value=int(df["Soma"].max()) if not df["Soma"].isna().all() else 300)
                            df = df[(df["Soma"] >= soma_min) & (df["Soma"] <= soma_max)]

                    with col2:
                        if "Pares/Ímpares" in df.columns:
                            par_impar_filter = st.selectbox("Filtrar por Pares/Ímpares:", ["Todos"] + df["Pares/Ímpares"].dropna().unique().tolist())
                            if par_impar_filter != "Todos":
                                df = df[df["Pares/Ímpares"] == par_impar_filter]

                # Mostrar dados
                st.dataframe(df, use_container_width=True)

                # Estatísticas básicas
                if len(df) > 0:
                    st.subheader("📊 Estatísticas")
                    col1, col2, col3 = st.columns(3)

                    with col1:
                        st.metric("Registros mostrados", len(df))

                    if "Soma" in df.columns:
                        with col2:
                            st.metric("Soma média", f"{df['Soma'].mean():.1f}")
                        with col3:
                            st.metric("Soma mediana", f"{df['Soma'].median():.1f}")
            else:
                st.warning("Nenhum dado encontrado.")

# Inserir Dados
elif selected_menu == "➕ Inserir Dados":
    st.title("➕ Inserir Novos Dados")

    tables = db_manager.get_tables()
    if not tables:
        st.warning("Nenhuma tabela encontrada.")
    else:
        selected_table = st.selectbox("Selecione uma tabela:", tables)

        if selected_table:
            columns, _ = db_manager.get_table_info(selected_table)

            st.subheader(f"Inserir dados na tabela: {selected_table}")

            # Formulário dinâmico baseado nas colunas
            form_data = {}

            with st.form(f"insert_form_{selected_table}"):
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    is_pk = col[5] == 1  # Primary key

                    if is_pk and "AUTOINCREMENT" in col_type.upper():
                        st.info(f"Campo {col_name} é auto-incremento, será preenchido automaticamente.")
                        continue

                    # Interface específica para Loto Fácil
                    if col_name == "Resultado1" and "Planilha" in selected_table:
                        st.subheader("🎲 Números da Loto Fácil")
                        numbers_input = st.text_input(
                            "Digite 15 números (1-25) separados por vírgula:",
                            placeholder="01,02,03,04,05,06,07,08,09,10,11,12,13,14,15"
                        )

                        if numbers_input:
                            try:
                                numbers = [int(n.strip()) for n in numbers_input.split(",")]
                                is_valid, message = validate_lotofacil_game(numbers)

                                if is_valid:
                                    st.success(f"✅ {message}")
                                    form_data[col_name] = format_lotofacil_numbers(numbers)

                                    # Auto-calcular soma
                                    if any(c[1] == "Soma" for c in columns):
                                        form_data["Soma"] = sum(numbers)
                                        st.info(f"Soma calculada automaticamente: {sum(numbers)}")

                                    # Auto-calcular pares/ímpares
                                    if any(c[1] == "Pares/Ímpares" for c in columns):
                                        pares = sum(1 for n in numbers if n % 2 == 0)
                                        impares = 15 - pares
                                        form_data["Pares/Ímpares"] = f"{pares}P/{impares}I"
                                        st.info(f"Pares/Ímpares: {pares} pares, {impares} ímpares")
                                else:
                                    st.error(f"❌ {message}")
                            except ValueError:
                                st.error("❌ Formato inválido. Use números separados por vírgula.")

                    elif col_name in ["Soma", "Pares/Ímpares"] and "Planilha" in selected_table:
                        # Estes campos são calculados automaticamente
                        continue

                    elif "BOOLEAN" in col_type.upper():
                        form_data[col_name] = st.checkbox(f"{col_name}:")

                    elif "INTEGER" in col_type.upper():
                        form_data[col_name] = st.number_input(f"{col_name}:", value=0, step=1)

                    elif "REAL" in col_type.upper() or "FLOAT" in col_type.upper():
                        form_data[col_name] = st.number_input(f"{col_name}:", value=0.0, step=0.1)

                    elif "TIMESTAMP" in col_type.upper() or "DATE" in col_type.upper():
                        form_data[col_name] = st.date_input(f"{col_name}:")

                    else:  # TEXT
                        form_data[col_name] = st.text_input(f"{col_name}:")

                submitted = st.form_submit_button("💾 Inserir Dados")

                if submitted:
                    # Filtrar campos vazios e None
                    filtered_data = {k: v for k, v in form_data.items() if v is not None and v != ""}

                    if filtered_data:
                        # Construir query INSERT
                        columns_str = ", ".join(filtered_data.keys())
                        placeholders = ", ".join(["?" for _ in filtered_data])
                        query = f"INSERT INTO {selected_table} ({columns_str}) VALUES ({placeholders})"

                        success = db_manager.execute_command(query, list(filtered_data.values()))

                        if success:
                            st.success("✅ Dados inseridos com sucesso!")
                            st.balloons()
                        else:
                            st.error("❌ Erro ao inserir dados.")
                    else:
                        st.warning("⚠️ Preencha pelo menos um campo.")

# Editar Dados
elif selected_menu == "✏️ Editar Dados":
    st.title("✏️ Editar Dados")

    tables = db_manager.get_tables()
    if not tables:
        st.warning("Nenhuma tabela encontrada.")
    else:
        selected_table = st.selectbox("Selecione uma tabela:", tables)

        if selected_table:
            # Buscar registro para editar
            st.subheader("🔍 Buscar registro para editar")

            columns, total_count = db_manager.get_table_info(selected_table)

            # Opções de busca
            search_method = st.radio("Método de busca:", ["Por ID", "Por filtro"])

            if search_method == "Por ID":
                # Assumir que a primeira coluna é o ID
                id_column = columns[0][1]
                record_id = st.number_input(f"Digite o {id_column}:", min_value=1, step=1)

                if st.button("🔍 Buscar"):
                    query = f"SELECT * FROM {selected_table} WHERE {id_column} = ?"
                    df = db_manager.execute_query(query, [record_id])

                    if df is not None and not df.empty:
                        st.success("✅ Registro encontrado!")
                        st.dataframe(df)

                        # Formulário de edição
                        st.subheader("✏️ Editar registro")

                        with st.form("edit_form"):
                            edit_data = {}
                            record = df.iloc[0]

                            for col in columns:
                                col_name = col[1]
                                col_type = col[2]
                                is_pk = col[5] == 1

                                if is_pk:
                                    st.info(f"{col_name}: {record[col_name]} (chave primária - não editável)")
                                    continue

                                current_value = record[col_name]

                                if "BOOLEAN" in col_type.upper():
                                    edit_data[col_name] = st.checkbox(f"{col_name}:", value=bool(current_value))
                                elif "INTEGER" in col_type.upper():
                                    edit_data[col_name] = st.number_input(f"{col_name}:", value=int(current_value) if current_value else 0)
                                elif "REAL" in col_type.upper():
                                    edit_data[col_name] = st.number_input(f"{col_name}:", value=float(current_value) if current_value else 0.0)
                                else:
                                    edit_data[col_name] = st.text_input(f"{col_name}:", value=str(current_value) if current_value else "")

                            if st.form_submit_button("💾 Salvar Alterações"):
                                # Construir query UPDATE
                                set_clause = ", ".join([f"{k} = ?" for k in edit_data.keys()])
                                query = f"UPDATE {selected_table} SET {set_clause} WHERE {id_column} = ?"
                                params = list(edit_data.values()) + [record_id]

                                success = db_manager.execute_command(query, params)

                                if success:
                                    st.success("✅ Registro atualizado com sucesso!")
                                    st.rerun()
                    else:
                        st.error("❌ Registro não encontrado.")

# Deletar Dados
elif selected_menu == "🗑️ Deletar Dados":
    st.title("🗑️ Deletar Dados")
    st.warning("⚠️ **ATENÇÃO**: Esta operação é irreversível!")

    tables = db_manager.get_tables()
    if not tables:
        st.warning("Nenhuma tabela encontrada.")
    else:
        selected_table = st.selectbox("Selecione uma tabela:", tables)

        if selected_table:
            st.subheader(f"Deletar dados da tabela: {selected_table}")

            columns, total_count = db_manager.get_table_info(selected_table)

            delete_method = st.radio("Método de exclusão:", ["Por ID específico", "Por condição"])

            if delete_method == "Por ID específico":
                id_column = columns[0][1]  # Primeira coluna como ID
                record_id = st.number_input(f"Digite o {id_column} para deletar:", min_value=1, step=1)

                if st.button("🔍 Visualizar registro"):
                    query = f"SELECT * FROM {selected_table} WHERE {id_column} = ?"
                    df = db_manager.execute_query(query, [record_id])

                    if df is not None and not df.empty:
                        st.success("✅ Registro encontrado:")
                        st.dataframe(df)

                        if st.button("🗑️ CONFIRMAR EXCLUSÃO", type="primary"):
                            delete_query = f"DELETE FROM {selected_table} WHERE {id_column} = ?"
                            success = db_manager.execute_command(delete_query, [record_id])

                            if success:
                                st.success("✅ Registro deletado com sucesso!")
                                st.balloons()
                            else:
                                st.error("❌ Erro ao deletar registro.")
                    else:
                        st.error("❌ Registro não encontrado.")

            else:  # Por condição
                st.subheader("🔍 Deletar por condição")
                condition = st.text_input("Digite a condição WHERE (sem a palavra WHERE):",
                                        placeholder="Soma > 200 AND \"Pares/Ímpares\" = '8P/7I'")

                if condition:
                    # Primeiro, mostrar quantos registros serão afetados
                    preview_query = f"SELECT COUNT(*) as total FROM {selected_table} WHERE {condition}"
                    preview_df = db_manager.execute_query(preview_query)

                    if preview_df is not None:
                        count = preview_df.iloc[0]['total']
                        st.info(f"📊 Esta operação deletará **{count}** registros.")

                        if count > 0:
                            # Mostrar alguns exemplos
                            sample_query = f"SELECT * FROM {selected_table} WHERE {condition} LIMIT 5"
                            sample_df = db_manager.execute_query(sample_query)

                            if sample_df is not None and not sample_df.empty:
                                st.subheader("📋 Exemplos de registros que serão deletados:")
                                st.dataframe(sample_df)

                            confirm_text = st.text_input(f"Digite 'DELETAR {count}' para confirmar:")

                            if confirm_text == f"DELETAR {count}":
                                if st.button("🗑️ EXECUTAR EXCLUSÃO", type="primary"):
                                    delete_query = f"DELETE FROM {selected_table} WHERE {condition}"
                                    success = db_manager.execute_command(delete_query)

                                    if success:
                                        st.success(f"✅ {count} registros deletados com sucesso!")
                                    else:
                                        st.error("❌ Erro ao deletar registros.")

# Gerenciar Tabelas
elif selected_menu == "🏗️ Gerenciar Tabelas":
    st.title("🏗️ Gerenciar Tabelas")

    tab1, tab2, tab3 = st.tabs(["📋 Visualizar Estrutura", "➕ Criar Tabela", "🗑️ Deletar Tabela"])

    with tab1:
        st.subheader("📋 Estrutura das Tabelas")

        tables = db_manager.get_tables()
        if tables:
            selected_table = st.selectbox("Selecione uma tabela:", tables, key="structure_table")

            if selected_table:
                columns, count = db_manager.get_table_info(selected_table)

                st.info(f"Tabela: **{selected_table}** | Registros: **{count:,}**")

                # Mostrar estrutura
                structure_data = []
                for col in columns:
                    structure_data.append({
                        "Coluna": col[1],
                        "Tipo": col[2],
                        "Não Nulo": "Sim" if col[3] else "Não",
                        "Valor Padrão": col[4] if col[4] else "-",
                        "Chave Primária": "Sim" if col[5] else "Não"
                    })

                df_structure = pd.DataFrame(structure_data)
                st.dataframe(df_structure, use_container_width=True)

                # Mostrar SQL de criação
                if st.button("📄 Ver SQL de Criação"):
                    sql_query = f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{selected_table}'"
                    sql_df = db_manager.execute_query(sql_query)

                    if sql_df is not None and not sql_df.empty:
                        st.code(sql_df.iloc[0]['sql'], language='sql')

    with tab2:
        st.subheader("➕ Criar Nova Tabela")

        table_name = st.text_input("Nome da tabela:", placeholder="nova_tabela")

        if table_name:
            st.subheader("📝 Definir Colunas")

            # Interface para adicionar colunas
            if 'columns_list' not in st.session_state:
                st.session_state.columns_list = []

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                col_name = st.text_input("Nome da coluna:", key="new_col_name")

            with col2:
                col_type = st.selectbox("Tipo:", ["TEXT", "INTEGER", "REAL", "BOOLEAN", "TIMESTAMP"], key="new_col_type")

            with col3:
                is_primary = st.checkbox("Chave Primária", key="new_col_pk")
                not_null = st.checkbox("Não Nulo", key="new_col_nn")

            with col4:
                if st.button("➕ Adicionar Coluna"):
                    if col_name:
                        column_def = {
                            "name": col_name,
                            "type": col_type,
                            "primary_key": is_primary,
                            "not_null": not_null
                        }
                        st.session_state.columns_list.append(column_def)
                        st.rerun()

            # Mostrar colunas adicionadas
            if st.session_state.columns_list:
                st.subheader("📋 Colunas Definidas")

                for i, col in enumerate(st.session_state.columns_list):
                    col1, col2 = st.columns([4, 1])

                    with col1:
                        flags = []
                        if col['primary_key']:
                            flags.append("PK")
                        if col['not_null']:
                            flags.append("NOT NULL")

                        flags_str = f" ({', '.join(flags)})" if flags else ""
                        st.write(f"**{col['name']}** - {col['type']}{flags_str}")

                    with col2:
                        if st.button("🗑️", key=f"remove_col_{i}"):
                            st.session_state.columns_list.pop(i)
                            st.rerun()

                # Botão para criar tabela
                if st.button("🏗️ Criar Tabela", type="primary"):
                    # Construir SQL CREATE TABLE
                    columns_sql = []

                    for col in st.session_state.columns_list:
                        col_sql = f"{col['name']} {col['type']}"

                        if col['primary_key']:
                            col_sql += " PRIMARY KEY"
                            if col['type'] == "INTEGER":
                                col_sql += " AUTOINCREMENT"

                        if col['not_null'] and not col['primary_key']:
                            col_sql += " NOT NULL"

                        columns_sql.append(col_sql)

                    create_sql = f"CREATE TABLE {table_name} ({', '.join(columns_sql)})"

                    st.code(create_sql, language='sql')

                    success = db_manager.execute_command(create_sql)

                    if success:
                        st.success(f"✅ Tabela '{table_name}' criada com sucesso!")
                        st.session_state.columns_list = []  # Limpar lista
                        st.balloons()
                    else:
                        st.error("❌ Erro ao criar tabela.")

    with tab3:
        st.subheader("🗑️ Deletar Tabela")
        st.warning("⚠️ **ATENÇÃO**: Esta operação é irreversível e deletará todos os dados!")

        tables = db_manager.get_tables()
        if tables:
            table_to_delete = st.selectbox("Selecione uma tabela para deletar:", tables, key="delete_table")

            if table_to_delete:
                _, count = db_manager.get_table_info(table_to_delete)
                st.error(f"⚠️ A tabela '{table_to_delete}' contém {count:,} registros que serão perdidos!")

                confirm_text = st.text_input(f"Digite 'DELETAR {table_to_delete}' para confirmar:")

                if confirm_text == f"DELETAR {table_to_delete}":
                    if st.button("🗑️ DELETAR TABELA", type="primary"):
                        drop_sql = f"DROP TABLE {table_to_delete}"
                        success = db_manager.execute_command(drop_sql)

                        if success:
                            st.success(f"✅ Tabela '{table_to_delete}' deletada com sucesso!")
                            st.rerun()
# Análises
elif selected_menu == "📈 Análises":
    st.title("📈 Análises da Loto Fácil")

    tables = [t for t in db_manager.get_tables() if "Planilha" in t]

    if not tables:
        st.warning("Nenhuma tabela de jogos encontrada.")
    else:
        selected_table = st.selectbox("Selecione uma tabela para análise:", tables)

        if selected_table:
            # Análises básicas
            st.subheader("📊 Estatísticas Básicas")

            # Query para estatísticas
            stats_query = f"""
            SELECT
                COUNT(*) as total_jogos,
                MIN(Soma) as soma_min,
                MAX(Soma) as soma_max,
                AVG(Soma) as soma_media,
                COUNT(DISTINCT "Pares/Ímpares") as padroes_par_impar
            FROM {selected_table}
            WHERE Soma IS NOT NULL
            """

            stats_df = db_manager.execute_query(stats_query)

            if stats_df is not None and not stats_df.empty:
                stats = stats_df.iloc[0]

                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("Total de Jogos", f"{int(stats['total_jogos']):,}")

                with col2:
                    st.metric("Soma Mínima", int(stats['soma_min']))

                with col3:
                    st.metric("Soma Máxima", int(stats['soma_max']))

                with col4:
                    st.metric("Soma Média", f"{stats['soma_media']:.1f}")

            # Distribuição de somas
            st.subheader("📊 Distribuição de Somas")

            soma_query = f"SELECT Soma, COUNT(*) as frequencia FROM {selected_table} WHERE Soma IS NOT NULL GROUP BY Soma ORDER BY Soma"
            soma_df = db_manager.execute_query(soma_query)

            if soma_df is not None and not soma_df.empty:
                fig_soma = px.bar(
                    soma_df,
                    x="Soma",
                    y="frequencia",
                    title="Distribuição de Somas dos Jogos",
                    labels={"frequencia": "Frequência", "Soma": "Soma dos Números"}
                )
                st.plotly_chart(fig_soma, use_container_width=True)

            # Análise de Pares/Ímpares
            st.subheader("🎯 Análise Pares/Ímpares")

            par_impar_query = f"""
            SELECT "Pares/Ímpares", COUNT(*) as frequencia
            FROM {selected_table}
            WHERE "Pares/Ímpares" IS NOT NULL
            GROUP BY "Pares/Ímpares"
            ORDER BY frequencia DESC
            """

            par_impar_df = db_manager.execute_query(par_impar_query)

            if par_impar_df is not None and not par_impar_df.empty:
                col1, col2 = st.columns(2)

                with col1:
                    fig_pie = px.pie(
                        par_impar_df.head(10),
                        values="frequencia",
                        names="Pares/Ímpares",
                        title="Top 10 Padrões Pares/Ímpares"
                    )
                    st.plotly_chart(fig_pie, use_container_width=True)

                with col2:
                    st.dataframe(par_impar_df.head(15), use_container_width=True)

            # Análise de números mais frequentes
            st.subheader("🔢 Análise de Números")

            if st.button("🔍 Analisar Frequência de Números"):
                with st.spinner("Analisando números..."):
                    # Esta análise pode ser pesada, então limitamos a amostra
                    sample_query = f"SELECT Resultado1 FROM {selected_table} WHERE Resultado1 IS NOT NULL LIMIT 10000"
                    sample_df = db_manager.execute_query(sample_query)

                    if sample_df is not None and not sample_df.empty:
                        # Contar frequência de cada número
                        number_freq = {}

                        for resultado in sample_df['Resultado1']:
                            numbers = parse_lotofacil_numbers(resultado)
                            for num in numbers:
                                number_freq[num] = number_freq.get(num, 0) + 1

                        if number_freq:
                            freq_df = pd.DataFrame([
                                {"Número": num, "Frequência": freq}
                                for num, freq in sorted(number_freq.items())
                            ])

                            fig_freq = px.bar(
                                freq_df,
                                x="Número",
                                y="Frequência",
                                title=f"Frequência dos Números (amostra de {len(sample_df)} jogos)",
                                labels={"Frequência": "Frequência", "Número": "Número"}
                            )
                            st.plotly_chart(fig_freq, use_container_width=True)

                            # Top números
                            top_numbers = freq_df.nlargest(10, 'Frequência')
                            st.subheader("🏆 Top 10 Números Mais Frequentes")
                            st.dataframe(top_numbers, use_container_width=True)

# Import/Export
elif selected_menu == "💾 Import/Export":
    st.title("💾 Import/Export de Dados")

    tab1, tab2, tab3 = st.tabs(["📤 Exportar", "📥 Importar", "🔄 Backup"])

    with tab1:
        st.subheader("📤 Exportar Dados")

        tables = db_manager.get_tables()
        if tables:
            export_table = st.selectbox("Selecione uma tabela para exportar:", tables)

            if export_table:
                col1, col2 = st.columns(2)

                with col1:
                    export_format = st.selectbox("Formato:", ["CSV", "JSON", "Excel"])

                with col2:
                    limit_export = st.number_input("Limite de registros (0 = todos):", min_value=0, value=0)

                if st.button("📤 Exportar"):
                    query = f"SELECT * FROM {export_table}"
                    if limit_export > 0:
                        query += f" LIMIT {limit_export}"

                    df = db_manager.execute_query(query)

                    if df is not None and not df.empty:
                        if export_format == "CSV":
                            csv = df.to_csv(index=False)
                            st.download_button(
                                label="💾 Download CSV",
                                data=csv,
                                file_name=f"{export_table}.csv",
                                mime="text/csv"
                            )

                        elif export_format == "JSON":
                            json_str = df.to_json(orient='records', indent=2)
                            st.download_button(
                                label="💾 Download JSON",
                                data=json_str,
                                file_name=f"{export_table}.json",
                                mime="application/json"
                            )

                        elif export_format == "Excel":
                            # Para Excel, precisaríamos do openpyxl
                            st.info("📝 Exportação para Excel requer instalação do openpyxl")

                        st.success(f"✅ {len(df)} registros prontos para download!")

    with tab2:
        st.subheader("📥 Importar Dados")

        uploaded_file = st.file_uploader("Escolha um arquivo CSV", type="csv")

        if uploaded_file is not None:
            try:
                df = pd.read_csv(uploaded_file)
                st.success(f"✅ Arquivo carregado: {len(df)} registros, {len(df.columns)} colunas")

                st.subheader("👀 Preview dos dados")
                st.dataframe(df.head(), use_container_width=True)

                # Selecionar tabela de destino
                tables = db_manager.get_tables()
                target_table = st.selectbox("Tabela de destino:", ["Nova tabela"] + tables)

                if target_table == "Nova tabela":
                    new_table_name = st.text_input("Nome da nova tabela:")

                    if new_table_name and st.button("📥 Importar para Nova Tabela"):
                        # Criar tabela baseada no DataFrame
                        df.to_sql(new_table_name, db_manager.get_connection(), if_exists='replace', index=False)
                        st.success(f"✅ Dados importados para nova tabela '{new_table_name}'!")

                else:
                    if st.button("📥 Importar para Tabela Existente"):
                        # Verificar compatibilidade de colunas
                        existing_columns, _ = db_manager.get_table_info(target_table)
                        existing_col_names = [col[1] for col in existing_columns]

                        missing_cols = set(df.columns) - set(existing_col_names)
                        if missing_cols:
                            st.error(f"❌ Colunas não encontradas na tabela: {missing_cols}")
                        else:
                            df.to_sql(target_table, db_manager.get_connection(), if_exists='append', index=False)
                            st.success(f"✅ {len(df)} registros importados para '{target_table}'!")

            except Exception as e:
                st.error(f"❌ Erro ao processar arquivo: {e}")

    with tab3:
        st.subheader("🔄 Backup do Banco de Dados")

        if st.button("💾 Criar Backup Completo"):
            # Exportar todas as tabelas
            tables = db_manager.get_tables()
            backup_data = {}

            for table in tables:
                df = db_manager.execute_query(f"SELECT * FROM {table}")
                if df is not None:
                    backup_data[table] = df.to_dict('records')

            if backup_data:
                import json
                backup_json = json.dumps(backup_data, indent=2, default=str)

                st.download_button(
                    label="💾 Download Backup Completo",
                    data=backup_json,
                    file_name=f"backup_lotofacil_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )

                st.success(f"✅ Backup criado com {len(tables)} tabelas!")

# Configurações
elif selected_menu == "⚙️ Configurações":
    st.title("⚙️ Configurações")

    st.subheader("🗄️ Informações do Banco de Dados")

    # Informações do arquivo
    import os
    if os.path.exists("database.sqlite"):
        file_size = os.path.getsize("database.sqlite")
        st.info(f"📁 Arquivo: database.sqlite | Tamanho: {file_size / (1024*1024):.2f} MB")

    # Configurações de performance
    st.subheader("⚡ Performance")

    if st.button("🧹 Executar VACUUM"):
        success = db_manager.execute_command("VACUUM")
        if success:
            st.success("✅ VACUUM executado com sucesso! Banco otimizado.")
        else:
            st.error("❌ Erro ao executar VACUUM.")

    if st.button("📊 Atualizar Estatísticas"):
        success = db_manager.execute_command("ANALYZE")
        if success:
            st.success("✅ Estatísticas atualizadas!")
        else:
            st.error("❌ Erro ao atualizar estatísticas.")

    # SQL personalizado
    st.subheader("🔧 Executar SQL Personalizado")
    st.warning("⚠️ Use com cuidado! Comandos SQL podem modificar ou deletar dados.")

    custom_sql = st.text_area("Digite seu comando SQL:", height=100)

    if custom_sql:
        col1, col2 = st.columns(2)

        with col1:
            if st.button("🔍 Executar Query (SELECT)"):
                if custom_sql.strip().upper().startswith("SELECT"):
                    result = db_manager.execute_query(custom_sql)
                    if result is not None:
                        st.dataframe(result, use_container_width=True)
                else:
                    st.error("❌ Use apenas comandos SELECT aqui.")

        with col2:
            if st.button("⚡ Executar Comando (INSERT/UPDATE/DELETE)"):
                if not custom_sql.strip().upper().startswith("SELECT"):
                    success = db_manager.execute_command(custom_sql)
                    if success:
                        st.success("✅ Comando executado com sucesso!")
                    else:
                        st.error("❌ Erro ao executar comando.")
                else:
                    st.error("❌ Use comandos de modificação aqui.")

# Footer
st.sidebar.markdown("---")
st.sidebar.markdown("🎲 **Loto Fácil Database Manager**")
st.sidebar.markdown("Desenvolvido com Streamlit")
st.sidebar.markdown(f"📊 Versão 1.0")
