#!/usr/bin/env python3
"""
Script para exportar tabelas do SQLite para CSV e criar nova tabela jogos_finalizados
"""

import sqlite3
import pandas as pd
import os

def export_tables_to_csv():
    """Exporta todas as tabelas do banco para arquivos CSV"""
    
    # Conectar ao banco de dados
    conn = sqlite3.connect('database.sqlite')
    
    try:
        # Lista das tabelas a serem exportadas
        tables = ['Planilha1', 'Planilha2', 'Planilha3', 'Planilha4']
        
        print("Exportando tabelas para CSV...")
        
        for table_name in tables:
            # Ler dados da tabela
            df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
            
            # Nome do arquivo CSV
            csv_filename = f"{table_name}.csv"
            
            # Exportar para CSV
            df.to_csv(csv_filename, index=False, encoding='utf-8')
            
            print(f"✓ Tabela '{table_name}' exportada para '{csv_filename}'")
            print(f"  - {len(df)} registros exportados")
            print(f"  - Colunas: {list(df.columns)}")
            print()
        
    except Exception as e:
        print(f"Erro ao exportar tabelas: {e}")
    
    finally:
        conn.close()

def create_jogos_finalizados_table():
    """Cria a nova tabela jogos_finalizados"""
    
    # Conectar ao banco de dados
    conn = sqlite3.connect('database.sqlite')
    cursor = conn.cursor()
    
    try:
        # Verificar se a tabela já existe
        cursor.execute("""
            SELECT name FROM sqlite_master 
            WHERE type='table' AND name='jogos_finalizados'
        """)
        
        if cursor.fetchone():
            print("Tabela 'jogos_finalizados' já existe.")
            
            # Mostrar estrutura atual
            cursor.execute("PRAGMA table_info(jogos_finalizados)")
            columns = cursor.fetchall()
            print("Estrutura atual:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
        else:
            # Criar a nova tabela
            cursor.execute("""
                CREATE TABLE jogos_finalizados (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    jogo_id INTEGER,
                    finalizado BOOLEAN NOT NULL DEFAULT FALSE,
                    data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    data_finalizacao TIMESTAMP NULL
                )
            """)
            
            conn.commit()
            print("✓ Tabela 'jogos_finalizados' criada com sucesso!")
            print("Estrutura da nova tabela:")
            print("  - id (INTEGER PRIMARY KEY AUTOINCREMENT)")
            print("  - jogo_id (INTEGER)")
            print("  - finalizado (BOOLEAN) - indica se o jogo foi finalizado")
            print("  - data_criacao (TIMESTAMP) - quando o registro foi criado")
            print("  - data_finalizacao (TIMESTAMP) - quando o jogo foi finalizado")
            
    except Exception as e:
        print(f"Erro ao criar tabela: {e}")
        conn.rollback()
    
    finally:
        conn.close()

def show_database_summary():
    """Mostra um resumo do banco de dados após as operações"""
    
    conn = sqlite3.connect('database.sqlite')
    cursor = conn.cursor()
    
    try:
        print("\n" + "="*50)
        print("RESUMO DO BANCO DE DADOS")
        print("="*50)
        
        # Listar todas as tabelas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        for table in tables:
            table_name = table[0]
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"Tabela '{table_name}': {count} registros")
        
    except Exception as e:
        print(f"Erro ao gerar resumo: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    print("Iniciando processamento do banco de dados...")
    print()
    
    # 1. Exportar tabelas para CSV
    export_tables_to_csv()
    
    # 2. Criar nova tabela jogos_finalizados
    create_jogos_finalizados_table()
    
    # 3. Mostrar resumo
    show_database_summary()
    
    print("\nProcessamento concluído!")
    print("\nArquivos CSV criados:")
    for i in range(1, 5):
        filename = f"Planilha{i}.csv"
        if os.path.exists(filename):
            print(f"  - {filename}")
