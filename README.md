# 🎲 Loto Fácil Database Manager

Uma aplicação web completa desenvolvida em Streamlit para gerenciar e analisar dados da Loto Fácil armazenados em banco SQLite.

## 🚀 Funcionalidades

### 📊 Dashboard
- Visão geral do banco de dados
- Métricas de tabelas e registros
- Gráficos de distribuição

### 🔍 Visualização de Dados
- Navegação por todas as tabelas
- Filtros específicos para Loto Fácil
- Paginação e busca
- Filtros por soma e padrões pares/ímpares

### ➕ Inserção de Dados
- Formulários dinâmicos baseados na estrutura das tabelas
- Validação automática de jogos da Loto Fácil
- Cálculo automático de soma e padrões pares/ímpares
- Interface intuitiva para números da Loto Fácil

### ✏️ Edição de Dados
- Busca por ID ou filtros
- Formulários de edição dinâmicos
- Preservação de chaves primárias

### 🗑️ Exclusão de Dados
- Exclusão por ID específico
- Exclusão em lote por condições
- Confirmações de segurança
- Preview dos registros a serem deletados

### 🏗️ Gerenciamento de Tabelas
- **Visualizar Estrutura**: Ver colunas, tipos e constraints
- **Criar Tabelas**: Interface visual para criação de novas tabelas
- **Deletar Tabelas**: Remoção segura com confirmações

### 📈 Análises Avançadas
- Estatísticas básicas (soma mín/máx/média)
- Distribuição de somas dos jogos
- Análise de padrões pares/ímpares
- Frequência de números individuais
- Gráficos interativos com Plotly

### 💾 Import/Export
- **Exportar**: CSV, JSON (Excel em desenvolvimento)
- **Importar**: Upload de arquivos CSV
- **Backup**: Backup completo do banco em JSON

### ⚙️ Configurações
- Informações do banco de dados
- Otimização (VACUUM, ANALYZE)
- Execução de SQL personalizado
- Comandos de manutenção

## 🛠️ Instalação

1. **Clone ou baixe os arquivos**
2. **Instale as dependências**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Execute a aplicação**:
   ```bash
   streamlit run app.py
   ```

4. **Acesse no navegador**: `http://localhost:8501`

## 📋 Pré-requisitos

- Python 3.8+
- Arquivo `database.sqlite` no mesmo diretório
- Dependências listadas em `requirements.txt`

## 🎯 Estrutura do Banco

A aplicação foi desenvolvida especificamente para trabalhar com dados da Loto Fácil, esperando tabelas com estrutura similar a:

- **Resultado1**: String com números separados por vírgula (ex: "01,02,03,04,05,06,07,08,09,10,11,12,13,14,15")
- **Pares/Ímpares**: Padrão de distribuição (ex: "8P/7I")
- **Soma**: Soma total dos números do jogo

## 🔧 Funcionalidades Específicas da Loto Fácil

### Validação de Jogos
- Verifica se há exatamente 15 números
- Confirma que números estão entre 1 e 25
- Detecta números duplicados
- Formatação automática

### Cálculos Automáticos
- **Soma**: Soma automática dos 15 números
- **Pares/Ímpares**: Contagem automática de números pares e ímpares

### Análises Especializadas
- Distribuição de somas
- Padrões mais frequentes de pares/ímpares
- Números mais sorteados
- Estatísticas detalhadas

## 🚨 Segurança

- Confirmações obrigatórias para operações destrutivas
- Preview de dados antes de exclusões em lote
- Backup automático recomendado
- Validação de entrada de dados

## 📊 Tecnologias Utilizadas

- **Streamlit**: Interface web
- **Pandas**: Manipulação de dados
- **SQLite3**: Banco de dados
- **Plotly**: Gráficos interativos
- **NumPy**: Operações numéricas

## 🎮 Como Usar

1. **Dashboard**: Comece aqui para ter uma visão geral
2. **Visualizar Dados**: Explore suas tabelas existentes
3. **Inserir Dados**: Adicione novos jogos da Loto Fácil
4. **Análises**: Descubra padrões nos seus dados
5. **Backup**: Sempre faça backup antes de grandes modificações

## ⚠️ Avisos Importantes

- **Sempre faça backup** antes de operações de exclusão
- **Teste em ambiente de desenvolvimento** antes de usar em produção
- **Comandos SQL personalizados** devem ser usados com cuidado
- **Grandes volumes de dados** podem impactar a performance

## 🆘 Suporte

Para problemas ou dúvidas:
1. Verifique se todas as dependências estão instaladas
2. Confirme que o arquivo `database.sqlite` existe
3. Verifique os logs no terminal do Streamlit
4. Use a funcionalidade de SQL personalizado para diagnósticos

---

**Desenvolvido para análise e gerenciamento de dados da Loto Fácil** 🎲
