#!/usr/bin/env python3
"""
Script de teste para verificar se a aplicação Streamlit está funcionando
"""

import sys
import sqlite3
import os

def test_dependencies():
    """Testa se todas as dependências estão instaladas"""
    print("🔍 Testando dependências...")
    
    try:
        import streamlit as st
        print("✅ Streamlit: OK")
    except ImportError:
        print("❌ Streamlit: NÃO INSTALADO")
        return False
    
    try:
        import pandas as pd
        print("✅ Pandas: OK")
    except ImportError:
        print("❌ Pandas: NÃO INSTALADO")
        return False
    
    try:
        import plotly.express as px
        print("✅ Plotly: OK")
    except ImportError:
        print("❌ Plotly: NÃO INSTALADO")
        return False
    
    try:
        import numpy as np
        print("✅ NumPy: OK")
    except ImportError:
        print("❌ NumPy: NÃO INSTALADO")
        return False
    
    return True

def test_database():
    """Testa se o banco de dados está acessível"""
    print("\n🗄️ Testando banco de dados...")
    
    if not os.path.exists("database.sqlite"):
        print("❌ Arquivo database.sqlite não encontrado!")
        return False
    
    try:
        conn = sqlite3.connect("database.sqlite")
        cursor = conn.cursor()
        
        # Listar tabelas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"✅ Banco conectado com sucesso!")
        print(f"📋 Tabelas encontradas: {len(tables)}")
        
        for table in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table[0]}")
            count = cursor.fetchone()[0]
            print(f"   - {table[0]}: {count:,} registros")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Erro ao conectar ao banco: {e}")
        return False

def test_app_syntax():
    """Testa se o arquivo app.py tem sintaxe válida"""
    print("\n🐍 Testando sintaxe do app.py...")
    
    if not os.path.exists("app.py"):
        print("❌ Arquivo app.py não encontrado!")
        return False
    
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            code = f.read()
        
        compile(code, "app.py", "exec")
        print("✅ Sintaxe do app.py: OK")
        return True
        
    except SyntaxError as e:
        print(f"❌ Erro de sintaxe no app.py: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro ao ler app.py: {e}")
        return False

def main():
    print("🎲 TESTE DA APLICAÇÃO LOTO FÁCIL")
    print("=" * 50)
    
    # Testes
    deps_ok = test_dependencies()
    db_ok = test_database()
    syntax_ok = test_app_syntax()
    
    print("\n" + "=" * 50)
    print("📊 RESUMO DOS TESTES")
    print("=" * 50)
    
    if deps_ok and db_ok and syntax_ok:
        print("✅ TODOS OS TESTES PASSARAM!")
        print("\n🚀 Para executar a aplicação:")
        print("   streamlit run app.py")
        print("\n🌐 A aplicação será aberta em: http://localhost:8501")
        return True
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
        
        if not deps_ok:
            print("\n📦 Para instalar dependências:")
            print("   pip install streamlit pandas plotly numpy")
        
        if not db_ok:
            print("\n🗄️ Verifique se o arquivo database.sqlite está no diretório correto")
        
        if not syntax_ok:
            print("\n🐍 Corrija os erros de sintaxe no app.py")
        
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
