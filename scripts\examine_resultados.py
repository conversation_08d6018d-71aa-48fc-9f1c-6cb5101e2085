#!/usr/bin/env python3
"""
Script para examinar o arquivo resultados_ate_hoje.sqlite
"""

import sqlite3
import pandas as pd

def examine_database():
    print("=== EXAMINANDO resultados_ate_hoje.sqlite ===")
    
    try:
        conn = sqlite3.connect('resultados_ate_hoje.sqlite')
        cursor = conn.cursor()
        
        # Listar tabelas
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"Tabelas encontradas: {len(tables)}")
        
        for table in tables:
            table_name = table[0]
            print(f"\n📋 Tabela: {table_name}")
            
            # Estrutura da tabela
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            print("Colunas:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # Contagem de registros
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"Registros: {count:,}")
            
            # Mostrar algumas linhas de exemplo
            if count > 0:
                print("Primeiras 3 linhas:")
                df = pd.read_sql_query(f"SELECT * FROM {table_name} LIMIT 3", conn)
                print(df.to_string())
        
        conn.close()
        return tables
        
    except Exception as e:
        print(f"Erro ao examinar banco: {e}")
        return []

if __name__ == "__main__":
    examine_database()
