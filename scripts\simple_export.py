import sqlite3
import csv

# Conectar ao banco
conn = sqlite3.connect('database.sqlite')
cursor = conn.cursor()

# Exportar cada tabela
tables = ['Planilha1', 'Planilha2', 'Planilha3', 'Planilha4']

for table in tables:
    print(f"Exportando {table}...")
    
    # Obter dados
    cursor.execute(f"SELECT * FROM {table}")
    data = cursor.fetchall()
    
    # Obter nomes das colunas
    cursor.execute(f"PRAGMA table_info({table})")
    columns = [col[1] for col in cursor.fetchall()]
    
    # Escrever CSV
    with open(f"{table}.csv", 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(columns)  # Cabeçalho
        writer.writerows(data)    # Dados
    
    print(f"✓ {table}.csv criado com {len(data)} registros")

# Criar nova tabela
print("\nCriando tabela jogos_finalizados...")
try:
    cursor.execute("""
        CREATE TABLE IF NOT EXISTS jogos_finalizados (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            jogo_id INTEGER,
            finalizado BOOLEAN NOT NULL DEFAULT 0,
            data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    """)
    conn.commit()
    print("✓ Tabela jogos_finalizados criada!")
except Exception as e:
    print(f"Erro: {e}")

conn.close()
print("\nConcluído!")
